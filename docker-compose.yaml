version: '3.8'

services:
  # Base image service
  steampipe-base:
    build:
      context: .
      dockerfile: Base_image_steampipe/Dockerfile
    image: steampipe-base:latest
    command: echo "Base image built"

  # Admin API Service
  admin-api:
    build:
      context: .
      dockerfile: custom/services/admin/Dockerfile
    container_name: admin-api
    ports:
      - "3000:8080"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - API_PORT=8080
      - AUTH_TYPE=${AUTH_TYPE:-adc}
      - CLIENT_NAME=${CLIENT_NAME:-steampipe-compliance}
      - GCP_PROJECT_ID=${GCP_PROJECT_ID:-}
      - GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS:-/home/<USER>/.config/gcloud/application_default_credentials.json}
      - DATABASE_URL=${DATABASE_URL:-}
      - REDIS_URL=${REDIS_URL:-}
      - JWT_SECRET=${JWT_SECRET:-}
      - CORS_ORIGIN=${CORS_ORIGIN:-*}
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-900000}
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-100}
      - DOMAIN=${DOMAIN:-localhost}
      - CUSTOMER_ID=${CUSTOMER_ID:-local}
    volumes:
      - ./custom/services/admin:/app/admin
      - ./custom/shared:/app/shared
      - ./custom/services/admin/config:/app/config:ro
      - ./custom/services/admin/logs:/app/logs
      - ./custom/services/admin/uploads:/app/uploads
      - admin-api-data:/app/data
      - ${HOME}/.config/gcloud:/home/<USER>/.config/gcloud:ro
    networks:
      - app-network

  # GCP Compliance Service
  steampipe-gcp:
    build:
      context: .
      dockerfile: custom/services/gcp/Dockerfile
      args:
        - BASE_IMAGE=steampipe-base:latest
    container_name: steampipe-gcp-compliance
    depends_on:
      - steampipe-base
    volumes:
      - ./custom/services/gcp:/app/gcp
      - ./custom/shared:/app/shared
      - ./opensource/mods/gcp-compliance:/app/steampipe-mod-gcp-compliance
      - gcp-steampipe-internal:/home/<USER>/.steampipe/internal
      - gcp-steampipe-logs:/home/<USER>/.steampipe/logs
      - ./custom/services/gcp/steampipe-config:/home/<USER>/.steampipe/config
      - gcp-powerpipe-config:/home/<USER>/.powerpipe
      - gcp-steampipe-db:/home/<USER>/.steampipe/db
      - gcp-steampipe-plugins:/home/<USER>/.steampipe/plugins
      - ${HOME}/.config/gcloud:/home/<USER>/.config/gcloud:ro
    working_dir: /app
    ports:
      - "9195:9194"
      - "9034:9033"
      - "9196:9193"
      - "8080:8080"
    environment:
      - STEAMPIPE_UPDATE_CHECK=false
      - POWERPIPE_UPDATE_CHECK=false
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - STEAMPIPE_DATABASE_USER=steampipe
      - STEAMPIPE_DATABASE_NAME=steampipe
      - GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/.config/gcloud/application_default_credentials.json
      - STEAMPIPE_INIT_TIMEOUT=120
      - STEAMPIPE_TIMEOUT=600s
      - AUTH_TYPE=adc
      - STEAMPIPE_DATABASE_LISTEN_ADDRESS=0.0.0.0:9193
      - PROJECT_ID=vratant-test-prj
      - DOMAIN=microservice.neosecai.dev
      - CLOUDSDK_CORE_PROJECT=vratant-test-prj
      - GCLOUD_PROJECT=vratant-test-prj
    tty: true
    stdin_open: true
    networks:
      - app-network

  steampipe-azure:
    build:
      context: .
      dockerfile: custom/services/azure/Dockerfile
      args:
        - BASE_IMAGE=steampipe-base:latest
    depends_on:
      - steampipe-base
    container_name: steampipe-azure-compliance
    volumes:
      - ./custom/services/azure:/app/azure
      - ./custom/shared:/app/shared
      - ./opensource/mods/azure-compliance:/app/steampipe-mod-azure-compliance
      - azure-steampipe-internal:/home/<USER>/.steampipe/internal
      - azure-steampipe-logs:/home/<USER>/.steampipe/logs
      - ./custom/services/azure/steampipe-config:/home/<USER>/.steampipe/config
      - azure-powerpipe-config:/home/<USER>/.powerpipe
      - azure-steampipe-db:/home/<USER>/.steampipe/db
      - azure-steampipe-plugins:/home/<USER>/.steampipe/plugins
      - ~/.azure:/home/<USER>/.azure
    working_dir: /app
    ports:
      - "9197:9194"
      - "9035:9033"
      - "9198:9193"
      - "8083:8083"
    environment:
      - STEAMPIPE_UPDATE_CHECK=false
      - POWERPIPE_UPDATE_CHECK=false
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - STEAMPIPE_DATABASE_USER=steampipe
      - STEAMPIPE_DATABASE_NAME=steampipe
      - STEAMPIPE_INIT_TIMEOUT=300
      - STEAMPIPE_TIMEOUT=600s
      - STEAMPIPE_DATABASE_TIMEOUT=300
      - DOMAIN=microservice.neosecai.dev
      - AUTH_TYPE=adc
      - CUSTOMER_ID=local
      - AZURE_CONFIG_DIR=/home/<USER>/.azure
    tty: true
    stdin_open: true
    networks:
      - app-network

  steampipe-aws:
    build:
      context: .
      dockerfile: custom/services/aws/Dockerfile
      args:
        - BASE_IMAGE=steampipe-base:latest
    depends_on:
      - steampipe-base
    container_name: steampipe-aws-compliance
    volumes:
      - ./custom/services/aws:/app/aws
      - ./custom/shared:/app/shared
      - ./opensource/mods/aws-compliance:/app/steampipe-mod-aws-compliance
      - aws-steampipe-internal:/home/<USER>/.steampipe/internal
      - aws-steampipe-logs:/home/<USER>/.steampipe/logs
      - ./custom/services/aws/steampipe-config:/home/<USER>/.steampipe/config
      - aws-powerpipe-config:/home/<USER>/.powerpipe
      - aws-steampipe-db:/home/<USER>/.steampipe/db
      - aws-steampipe-plugins:/home/<USER>/.steampipe/plugins
      - ${HOME}/.aws:/home/<USER>/.aws:ro
    working_dir: /app
    ports:
      - "9194:9194"
      - "9033:9033"
      - "9193:9193"
      - "8082:8082"
    environment:
      - STEAMPIPE_UPDATE_CHECK=false
      - POWERPIPE_UPDATE_CHECK=false
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - STEAMPIPE_DATABASE_USER=steampipe
      - STEAMPIPE_DATABASE_NAME=steampipe
      - AUTH_TYPE=adc
      - AWS_REGION=us-east-1
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_CONFIG_FILE=/home/<USER>/.aws/config
      - AWS_SHARED_CREDENTIALS_FILE=/home/<USER>/.aws/credentials
      - CUSTOMER_ID=local
      - DOMAIN=microservice.neosecai.dev
      - STEAMPIPE_INIT_TIMEOUT=300
      - STEAMPIPE_TIMEOUT=600s
      - STEAMPIPE_DATABASE_TIMEOUT=300
    tty: true
    stdin_open: true
    networks:
      - app-network

  # API Gateway Service
  api-gateway:
    build:
      context: api-gateway/
      dockerfile: Dockerfile
    container_name: api-gateway
    ports:
      - "8000:8081"
    environment:
      - GCP_BACKEND_URL=http://steampipe-gcp:8080
      - AWS_BACKEND_URL=http://steampipe-aws:8082
      - AZURE_BACKEND_URL=http://steampipe-azure:8083
      - ADMIN_BACKEND_URL=http://admin-api:8080
    networks:
      - app-network

  # Admin UI Service
  admin-ui:
    build:
      context: custom/admin-ui/
      dockerfile: Dockerfile
    container_name: admin-ui
    ports:
      - "3001:80"
    environment:
      - REACT_APP_API_BASE_URL=${REACT_APP_API_BASE_URL:-http://localhost:3000}
      - CLIENT_NAME=${CLIENT_NAME:-steampipe-compliance}
      - REACT_APP_ENVIRONMENT=${REACT_APP_ENVIRONMENT:-production}
      - REACT_APP_VERSION=${REACT_APP_VERSION:-1.0.0}
      - REACT_APP_TITLE=${REACT_APP_TITLE:-Admin Dashboard}
      - REACT_APP_THEME=${REACT_APP_THEME:-default}
      - REACT_APP_ENABLE_ANALYTICS=${REACT_APP_ENABLE_ANALYTICS:-false}
      - REACT_APP_DEBUG=${REACT_APP_DEBUG:-false}
      - REACT_APP_REFRESH_INTERVAL=${REACT_APP_REFRESH_INTERVAL:-30000}
      - REACT_APP_TIMEOUT=${REACT_APP_TIMEOUT:-10000}
      - REACT_APP_DOMAIN=${REACT_APP_DOMAIN:-localhost}
      - REACT_APP_CUSTOMER_ID=${REACT_APP_CUSTOMER_ID:-local}
    volumes:
      - ./custom/admin-ui:/app/admin-ui
      - ./custom/admin-ui/config:/usr/share/nginx/html/config:ro
      - ./custom/admin-ui/assets:/usr/share/nginx/html/assets:ro
      - admin-ui-logs:/var/log/nginx
      - admin-ui-data:/usr/share/nginx/html/data
    networks:
      - app-network

  # Dashboard UI Service (renamed from webapp)
  dashboard-ui:
    build:
      context: custom/dashboard/steampipe-secops-dashboard/frontend/
      dockerfile: Dockerfile
    container_name: dashboard-ui
    ports:
      - "8081:8081"
    environment:
      - BACKEND_GCP_URL=http://steampipe-gcp:8080
      - BACKEND_AWS_URL=http://steampipe-aws:8082
      - BACKEND_AZURE_URL=http://steampipe-azure:8083
      - CLIENT_NAME=${CLIENT_NAME:-steampipe-compliance}
      - REACT_APP_ENVIRONMENT=${REACT_APP_ENVIRONMENT:-production}
      - REACT_APP_VERSION=${REACT_APP_VERSION:-1.0.0}
      - REACT_APP_TITLE=${REACT_APP_TITLE:-Security Operations Dashboard}
      - REACT_APP_THEME=${REACT_APP_THEME:-dark}
      - REACT_APP_ENABLE_ANALYTICS=${REACT_APP_ENABLE_ANALYTICS:-true}
      - REACT_APP_DEBUG=${REACT_APP_DEBUG:-false}
      - REACT_APP_REFRESH_INTERVAL=${REACT_APP_REFRESH_INTERVAL:-30000}
      - REACT_APP_TIMEOUT=${REACT_APP_TIMEOUT:-10000}
      - REACT_APP_GCP_BACKEND_URL=${REACT_APP_GCP_BACKEND_URL:-http://localhost:8080}
      - REACT_APP_AWS_BACKEND_URL=${REACT_APP_AWS_BACKEND_URL:-http://localhost:8082}
      - REACT_APP_AZURE_BACKEND_URL=${REACT_APP_AZURE_BACKEND_URL:-http://localhost:8083}
      - REACT_APP_ENABLE_NOTIFICATIONS=${REACT_APP_ENABLE_NOTIFICATIONS:-true}
      - REACT_APP_AUTO_REFRESH=${REACT_APP_AUTO_REFRESH:-true}
      - REACT_APP_DOMAIN=${REACT_APP_DOMAIN:-localhost}
      - REACT_APP_CUSTOMER_ID=${REACT_APP_CUSTOMER_ID:-local}
      - NODE_ENV=${NODE_ENV:-production}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    volumes:
      - ./custom/dashboard/steampipe-secops-dashboard/frontend:/frontend
      - ./custom/dashboard/steampipe-secops-dashboard/frontend/config:/usr/share/nginx/html/config:ro
      - ./custom/dashboard/steampipe-secops-dashboard/frontend/assets:/usr/share/nginx/html/assets:ro
      - dashboard-ui-logs:/var/log/nginx
      - dashboard-ui-data:/usr/share/nginx/html/data
      - dashboard-ui-cache:/var/cache/nginx
    networks:
      - app-network

volumes:
  gcp-steampipe-config:
  gcp-steampipe-internal:
  gcp-steampipe-logs:
  gcp-powerpipe-config:
  gcp-steampipe-db:
  gcp-steampipe-plugins:
  azure-steampipe-config:
  azure-steampipe-internal:
  azure-steampipe-logs:
  azure-powerpipe-config:
  azure-steampipe-db:
  azure-steampipe-plugins:
  aws-steampipe-config:
  aws-steampipe-internal:
  aws-steampipe-logs:
  aws-powerpipe-config:
  aws-steampipe-db:
  aws-steampipe-plugins:
  admin-api-data:
  admin-ui-logs:
  admin-ui-data:
  dashboard-ui-logs:
  dashboard-ui-data:
  dashboard-ui-cache:

networks:
  app-network:
    driver: bridge